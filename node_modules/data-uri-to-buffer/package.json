{"name": "data-uri-to-buffer", "version": "2.0.2", "description": "Generate a Buffer instance from a Data URI string", "main": "index.js", "types": "index.d.ts", "scripts": {"test": "mocha --reporter spec"}, "repository": {"type": "git", "url": "git://github.com/TooTallNate/node-data-uri-to-buffer.git"}, "keywords": ["data", "uri", "datauri", "data-uri", "buffer", "convert", "rfc2397", "2397"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/node-data-uri-to-buffer/issues"}, "homepage": "https://github.com/TooTallNate/node-data-uri-to-buffer", "devDependencies": {"@types/node": "^8.0.7", "mocha": "^3.4.2"}}