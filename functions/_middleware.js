// --- НАЧАЛО КОНФИГУРАЦИИ PAGES FUNCTIONS ---
const OFFER_ID_LOG = "rhino-gold2"; // Используется в логах

const PATH_VARIANT_B = `/index_b.html`; 

// Конфигурация для A/B/n тестирования подвариантов для "доверенных" пользователей
// (тех, кто прошел все фильтры и не был отправлен на PATH_VARIANT_B)
const TRUSTED_USER_VARIANTS_CONFIG = {
    enabled: true, // Включить/выключить распределение на подварианты для доверенных
    identifierSource: 'ip_ua', // 'ip' или 'ip_ua'
    // ВАЖНО: Если enabled = true, должен быть хотя бы один вариант.
    // `/index_a.html` теперь является одним из вариантов в этой группе.
    variants: [
        { path: `/index_a1.html`,  weight: 100, name: "a1" },
    ]
};

const filterSettings = {
    FORCE_VARIANT_B: false,
    country: {
        enabled: true,
        logic: "allowed",
        list: ["IT"],
        sendUnknownToB: true,
    },
    asOrganization: {
        enabled: true,
        disallowed: [
            "Google", "Amazon", "AWS", "Microsoft", "Azure", "OVH", "Hetzner", "DigitalOcean", "Linode", "Cloudflare", "AS-CHOOPA", "Psychz", "FranTech", "Ace Data Centers", "Censys", "ColoCrossing", "Contabo", "Andhika Pratama Sanggoro", "Driftnet", "Facebook", "Iway", "Kprohost", "Maroc Telecom", "MXCLOUD", "NordVPN", "Octopus Web", "Onyphe", "ReliableSite", "Saygin Host", "Sovy Cloud", "sprint", "AMAZON-02", "MICROSOFT-CORP-MSN-AS-BLOCK"
        ],
    },
    clientTrust: {
        enabled: false,
        blockIfScoreLessThan: 15,
    },
    os: {
        enabled: true,
        logic: "allowed",
        list: ["Android","iOS" ],
    },
    isWebview: {
        enabled: true,
        required: true,
    },
    isFacebookApp: {
        enabled: true,
        required: true,
    }
};
// --- КОНЕЦ КОНФИГУРАЦИИ WORKER'А ---

// Вспомогательная функция для хэширования строки в число
async function getNumericHash(inputString) {
    const encoder = new TextEncoder();
    const data = encoder.encode(inputString);
    const hashBuffer = await crypto.subtle.digest('SHA-1', data);
    const hashArray = new Uint8Array(hashBuffer);
    let value = 0;
    for (let i = 0; i < 4; i++) {
        value = (value << 8) | hashArray[i];
    }
    return Math.abs(value);
}

// Функция выбора подварианта на основе хэша и весов
async function chooseWeightedSubVariantConfig(identifier, config) {
    // Возвращает объект { path: "...", name: "..." } или null, если не выбран
    if (!config.enabled || !config.variants || config.variants.length === 0) {
        console.warn("[chooseWeightedSubVariantConfig] Sub-variant splitting is disabled or no variants configured.");
        return null;
    }
    if (config.variants.length === 1) {
        return config.variants[0]; // Возвращаем весь объект варианта
    }

    const userHash = await getNumericHash(identifier);
    const totalWeight = config.variants.reduce((sum, variant) => sum + variant.weight, 0);

    if (totalWeight === 0) {
        console.warn("[chooseWeightedSubVariantConfig] Total weight for sub-variants is 0. Cannot choose.");
        return null; // Не можем выбрать, если сумма весов 0
    }

    let hashThreshold = userHash % totalWeight;

    for (const variant of config.variants) {
        if (hashThreshold < variant.weight) {
            return variant; // Возвращаем весь объект варианта {path, weight, name}
        }
        hashThreshold -= variant.weight;
    }
    // Запасной вариант (не должен срабатывать при корректной сумме весов > 0)
    console.warn("[chooseWeightedSubVariantConfig] Fallback: choosing last variant due to unexpected hashThreshold.");
    return config.variants.length > 0 ? config.variants[config.variants.length - 1] : null;
}


// Главная функция middleware для Pages Functions
export async function onRequest(context) {
    const { request, env, waitUntil, next } = context;
    const url = new URL(request.url);
    const path = url.pathname;

    // Основные пути, активирующие логику.
    // Если пользователь запросит /index_a1.html напрямую, он тоже пройдет эту логику.
    const baseAbTestPaths = ["/", "/index", "/index.html", "/index_b", "/index_a", "/index_a1", "/index_a2", "/index_a3", "/index_b.html", "/index_a.html", "/index_a1.html", "/index_a2.html", "/index_a3.html"]; // Общие точки входа (убрали лишние запятые)
    const dynamicAbTestPaths = TRUSTED_USER_VARIANTS_CONFIG.variants.map(v => v.path);
    const abTestPaths = [...new Set([...baseAbTestPaths, ...dynamicAbTestPaths, PATH_VARIANT_B])]; // Уникальный список

    if (abTestPaths.includes(path)) {
            const timestamp = new Date().toISOString();
            const cfRay = request.headers.get('cf-ray') || `no-ray-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            const requestUrl = request.url;

            const cfData = request.cf || {};
            const countryCode = cfData.country || null;
            const clientIp = request.headers.get('CF-Connecting-IP') || "UNKNOWN_IP";
            const asOrganization = cfData.asOrganization || "Unknown AS Organization";
            const clientTrustScore = cfData.clientTrustScore || null;
            const userAgentRaw = request.headers.get('User-Agent') || "";

            const headersObject = {};
            for (const [key, value] of request.headers) { headersObject[key] = value; }
            const allHeadersRaw = JSON.stringify(headersObject);
            const cfObjectRaw = JSON.stringify(cfData);
            const uaInfo = parseUserAgent(userAgentRaw);
            let clientTrustCategory = "unknown";
            if (clientTrustScore !== null) {
                if (clientTrustScore < 15) clientTrustCategory = "very_low_trust";
                else if (clientTrustScore < 50) clientTrustCategory = "low_trust";
                else clientTrustCategory = "high_trust";
            }

            let targetVariantPath;
            let filterPassedReason = "initial";
            let variantDecisionForLog; // Это будет "b", "a", "a1", "a2" и т.д.

            if (filterSettings.FORCE_VARIANT_B) {
                targetVariantPath = PATH_VARIANT_B;
                filterPassedReason = "forced_variant_b";
                variantDecisionForLog = "b";
            } else {
                let reasonForB = null;
                // --- Начало вашей существующей логики фильтрации ---
                if (filterSettings.country.enabled) {
                    const countryKnown = countryCode && countryCode !== "T1";
                    if (!countryKnown && filterSettings.country.sendUnknownToB) {
                        reasonForB = "country_unknown";
                    } else if (countryKnown) {
                        const listContainsCountry = filterSettings.country.list.includes(countryCode);
                        if (filterSettings.country.logic === "allowed" && !listContainsCountry) {
                            reasonForB = "country_not_in_allowed_list";
                        } else if (filterSettings.country.logic === "disallowed" && listContainsCountry) {
                            reasonForB = "country_in_disallowed_list";
                        }
                    }
                }
                if (!reasonForB && filterSettings.asOrganization.enabled && asOrganization) {
                    const lowerCaseAsOrg = asOrganization.toLowerCase();
                    if (filterSettings.asOrganization.disallowed.some(org => lowerCaseAsOrg.includes(org.toLowerCase()))) {
                        reasonForB = "as_organization_blocked";
                    }
                }
                if (!reasonForB && filterSettings.clientTrust.enabled && clientTrustScore !== null) {
                    if (clientTrustScore < filterSettings.clientTrust.blockIfScoreLessThan) {
                        reasonForB = `client_trust_score_too_low (${clientTrustScore})`;
                    }
                }
                if (!reasonForB && filterSettings.os.enabled && uaInfo.os.name !== "unknown") {
                    const lowerCaseOSList = filterSettings.os.list.map(os => os.toLowerCase());
                    const currentOSLower = uaInfo.os.name.toLowerCase();
                    const listContainsOS = lowerCaseOSList.includes(currentOSLower);
                    if (filterSettings.os.logic === "allowed" && !listContainsOS) {
                        reasonForB = "os_not_in_allowed_list";
                    } else if (filterSettings.os.logic === "disallowed" && listContainsOS) {
                        reasonForB = "os_in_disallowed_list";
                    }
                }
                if (!reasonForB && filterSettings.isWebview.enabled) {
                    if (uaInfo.browser.isWebview !== filterSettings.isWebview.required) {
                        reasonForB = `is_webview_mismatch (required: ${filterSettings.isWebview.required}, actual: ${uaInfo.browser.isWebview})`;
                    }
                }
                if (!reasonForB && filterSettings.isFacebookApp.enabled && filterSettings.isWebview.required && uaInfo.browser.isWebview) {
                     if (uaInfo.browser.isFacebookApp !== filterSettings.isFacebookApp.required) {
                        reasonForB = `facebook_app_mismatch (required: ${filterSettings.isFacebookApp.required}, actual: ${uaInfo.browser.isFacebookApp})`;
                    }
                }
                // --- Конец вашей существующей логики фильтрации ---

                if (reasonForB) {
                    targetVariantPath = PATH_VARIANT_B;
                    filterPassedReason = reasonForB;
                    variantDecisionForLog = "b";
                } else {
                    // Пользователь прошел все фильтры - это "доверенный" трафик.
                    filterPassedReason = "passed_all_filters";
                    let chosenTrustedVariant = null;

                    if (TRUSTED_USER_VARIANTS_CONFIG.enabled &&
                        TRUSTED_USER_VARIANTS_CONFIG.variants &&
                        TRUSTED_USER_VARIANTS_CONFIG.variants.length > 0) {

                        let identifier = (TRUSTED_USER_VARIANTS_CONFIG.identifierSource === 'ip_ua')
                            ? (clientIp + userAgentRaw)
                            : clientIp;

                        chosenTrustedVariant = await chooseWeightedSubVariantConfig(identifier, TRUSTED_USER_VARIANTS_CONFIG);
                    }

                    if (chosenTrustedVariant && chosenTrustedVariant.path) {
                        targetVariantPath = chosenTrustedVariant.path;
                        variantDecisionForLog = chosenTrustedVariant.name || chosenTrustedVariant.path.substring(chosenTrustedVariant.path.lastIndexOf('/') + 1).replace('.html', '').replace('index_', ''); // Используем name из конфига, или генерируем
                        filterPassedReason += `_to_split_${variantDecisionForLog}`;
                    } else {
                        // КРИТИЧЕСКАЯ СИТУАЦИЯ: Доверенный пользователь, но не удалось выбрать вариант A/B/n.
                        // Это может случиться, если TRUSTED_USER_VARIANTS_CONFIG.enabled = false или variants пуст/некорректен.
                        // Отправляем на B как безопасный fallback, или показываем ошибку.
                        // Для продакшена лучше иметь четкий fallback. Пока отправлю на B.
                        console.error(`[${cfRay}] CRITICAL: Trusted user, but no sub-variant chosen. Fallback to B. Check TRUSTED_USER_VARIANTS_CONFIG.`);
                        targetVariantPath = PATH_VARIANT_B; // Безопасный fallback
                        filterPassedReason = "trusted_user_no_subvariant_fallback_to_b";
                        variantDecisionForLog = "b_fallback_trusted";
                    }
                }
            }

            // Используем env.ASSETS.fetch() для получения локального файла
            let responseToClient;

            try {
                // Создаем URL для локального файла
                const assetUrl = new URL(targetVariantPath, request.url);
                const assetResponse = await env.ASSETS.fetch(assetUrl.toString());

                if (assetResponse.ok) {
                    responseToClient = new Response(assetResponse.body, assetResponse);
                    responseToClient.headers.set("Content-Type", "text/html;charset=UTF-8");
                } else {
                    filterPassedReason = `asset_fetch_error_(${assetResponse.status})_for_path_${targetVariantPath}`;
                    variantDecisionForLog = `error_fetch_${assetResponse.status}`;
                    console.error(`[${cfRay}] Asset fetch error ${assetResponse.status} for ${targetVariantPath}.`);
                    responseToClient = new Response(
                        `Error: Content not found (status ${assetResponse.status}) for ${targetVariantPath}. Ray ID: ${cfRay}`,
                        { status: assetResponse.status, headers: { "Content-Type": "text/html;charset=UTF-8" } }
                    );
                }
            } catch (error) {
                console.error(`[${cfRay}] Error fetching asset ${targetVariantPath}: ${error.message}`, error.stack);
                filterPassedReason = `asset_fetch_exception_for_path_${targetVariantPath}`;
                variantDecisionForLog = `error_exception`;
                responseToClient = new Response(
                    `Server Error. Ray ID: ${cfRay}`,
                    { status: 500, headers: { "Content-Type": "text/html;charset=UTF-8" } }
                );
            }

            waitUntil(
                logVisit(env, {
                    timestamp, cfRay, offerId: OFFER_ID_LOG,
                    variantShown: variantDecisionForLog,
                    countryCode, clientIp, clientTrustCategory, asOrganization,
                    deviceType: uaInfo.device.type, isWebview: uaInfo.browser.isWebview,
                    webviewAppGuess: uaInfo.browser.webviewAppName, osName: uaInfo.os.name,
                    browserName: uaInfo.browser.name, userAgentRaw, allHeadersRaw,
                    cfObjectRaw, requestUrl, filterPassedReason
                })
            );
            return responseToClient;
        } else {
            console.log(`[${request.headers.get('cf-ray') || 'NO-RAY'}] Passing through request for path: ${path} (URL: ${request.url}) to be handled by Pages.`);
            return await next();
        }
}

// --- logVisit и parseUserAgent остаются без изменений из вашего скрипта ---
async function logVisit(env, data) {
    try {
        const stmt = env.USER_TRACKING_DB.prepare(
            `INSERT INTO user_visits_log (
                timestamp, cf_ray, offer_id, variant_shown, country_code, client_ip, 
                client_trust_category, as_organization, device_type, is_webview, webview_app_guess, 
                os_name, browser_name, user_agent_raw, all_headers_raw, cf_object_raw, 
                request_url, filter_passed_reason
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`
        );
        await stmt.bind(
            data.timestamp, data.cfRay, data.offerId, data.variantShown, data.countryCode, data.clientIp,
            data.clientTrustCategory, data.asOrganization, data.deviceType, data.isWebview, data.webviewAppGuess,
            data.osName, data.browserName, data.userAgentRaw, data.allHeadersRaw, data.cfObjectRaw,
            data.requestUrl, data.filterPassedReason
        ).run();
    } catch (dbError) {
        console.error(`[${data.cfRay}] D1 Insert Error: ${dbError.message}`, dbError.cause ? dbError.cause.message : '', dbError.stack);
    }
}

function parseUserAgent(ua) {
    const result = {
        browser: { name: "unknown", version: "unknown", isWebview: false, webviewAppName: "N/A", isFacebookApp: false },
        os: { name: "unknown", version: "unknown" },
        device: { type: "unknown", vendor: "unknown", model: "unknown" },
    };

    if (!ua || typeof ua !== 'string') return result;

    // Facebook App User Agents
    if (/FBAN|FBAV|FB_IAB|FB4A|FBPN\/graph\.facebook\.katana|FBOP\/1|FBSN\/|FBMS\/|Messenger|Instagram|FBMF\/|FBBR\/|FBBD\/|FBBV\/|FBSV\/|FBCR\/|FBDM\/|FBBLK\/|FBLC\/|FBES\/|FBMA\/|FBCA\/|FBCT\/|FBCN\/|FBIOS\b/i.test(ua)) {
        result.browser.isWebview = true;
        result.browser.isFacebookApp = true;
        if (/Messenger|Orca-Android|FBAN\/MessengerForiOS/i.test(ua)) {
            result.browser.webviewAppName = "Facebook Messenger";
        } else if (/Instagram/i.test(ua)) {
            result.browser.webviewAppName = "Instagram";
        } else {
            result.browser.webviewAppName = "Facebook";
        }
        const fbavMatch = ua.match(/FBAV\/([\d\.]+)/i);
        if (fbavMatch) result.browser.version = fbavMatch[1];
    }
    
    // General WebView (Android)
    if (!result.browser.isFacebookApp && /(; wv\)|Mobile\).*wv|WebView|Crosswalk)/i.test(ua)) {
        result.browser.isWebview = true;
        if (result.browser.webviewAppName === "N/A") {
             result.browser.webviewAppName = "Android System WebView";
        }
    }
     // Google Search App (GSA)
    if (/GSA\/([\d\.]+)/i.test(ua)) {
        result.browser.isWebview = true;
        result.browser.webviewAppName = "Google Search App";
        result.browser.name = "Google Search App";
        result.browser.version = RegExp.$1;
    }


    // OS Detection
    if (/Windows NT 10\.0/i.test(ua)) { result.os.name = "Windows"; result.os.version = "10"; }
    else if (/Windows NT 6\.3/i.test(ua)) { result.os.name = "Windows"; result.os.version = "8.1"; }
    else if (/Windows NT 6\.2/i.test(ua)) { result.os.name = "Windows"; result.os.version = "8"; }
    else if (/Windows NT 6\.1/i.test(ua)) { result.os.name = "Windows"; result.os.version = "7"; }
    else if (/Windows Phone (?:OS )?([\d\.]+)/i.test(ua)) { result.os.name = "Windows Phone"; result.os.version = RegExp.$1; }
    else if (/Android(?:[\s\/]([\d\.]+))?/i.test(ua)) { result.os.name = "Android"; if(RegExp.$1) result.os.version = RegExp.$1; }
    else if (/(iPhone|iPad|iPod)(?:.*OS ([\d_]+))?/i.test(ua)) {
        result.os.name = "iOS";
        if(RegExp.$2) result.os.version = RegExp.$2.replace(/_/g, '.');
        result.device.vendor = "Apple";
        result.device.model = RegExp.$1;
    }
    else if (/(Mac OS X |Macintosh;.* OS X )([\d_]+)/i.test(ua) ) { result.os.name = "macOS"; result.os.version = RegExp.$2.replace(/_/g, '.'); }
    else if (/CrOS/i.test(ua)) { result.os.name = "Chrome OS"; }
    else if (/Linux/i.test(ua) && !/Android/i.test(ua)) { result.os.name = "Linux"; }


    // Browser Detection
    if (result.browser.name === "unknown" || (result.browser.isWebview && result.browser.webviewAppName === "Android System WebView") || (result.browser.isWebview && result.browser.webviewAppName === "N/A" && result.os.name === "iOS") ) {
        if (/(?:Edg|Edge|EdgA|EdgiOS)\/([\d\.]+)/i.test(ua)) { result.browser.name = "Edge"; result.browser.version = RegExp.$1; }
        else if (/(?:OPR|Opera)[\s\/]([\d\.]+)/i.test(ua)) { result.browser.name = "Opera"; result.browser.version = RegExp.$1; }
        else if (/(?:Chrome|CriOS|CrMo)\/([\d\.]+)/i.test(ua)) { result.browser.name = "Chrome"; result.browser.version = RegExp.$1; }
        else if (/(?:Firefox|FxiOS|Focus)\/([\d\.]+)/i.test(ua)) { result.browser.name = "Firefox"; result.browser.version = RegExp.$1; }
        else if (/MSIE ([\d\.]+)/i.test(ua) || /Trident\/.*; rv:([\d\.]+)/i.test(ua) ) { result.browser.name = "Internet Explorer"; result.browser.version = RegExp.$1 || RegExp.$2; }
        else if (/Version\/([\d\.]+).*Safari\//i.test(ua)) { result.browser.name = "Safari"; result.browser.version = RegExp.$1; }
        
        if (result.os.name === "Android" && result.browser.name === "Chrome" && /\bwv\b/.test(ua)) {
            result.browser.isWebview = true;
            result.browser.webviewAppName = "Android System WebView";
        }
        if (result.os.name === "iOS" && !result.browser.isFacebookApp && !/(CriOS|FxiOS|EdgiOS|OPiOS|Focus|GSA)/i.test(ua) ) {
            if (result.browser.name !== "Safari" && result.browser.name !== "unknown" ) {
                 result.browser.isWebview = true;
                 if(result.browser.webviewAppName === "N/A") result.browser.webviewAppName = `${result.browser.name} (WebView)`;
            }
        }
    }

    // Device Type
    if (/Mobi/i.test(ua) || result.os.name === "Android" || result.os.name === "iOS" || result.os.name === "Windows Phone") {
        if (/Tablet|iPad|PlayBook/i.test(ua) || (result.os.name === "Android" && !/Mobile/i.test(ua))) {
            result.device.type = "tablet";
        } else {
            result.device.type = "mobile";
        }
    } else if (result.os.name === "Windows" || result.os.name === "macOS" || result.os.name === "Linux" || result.os.name === "Chrome OS") {
        result.device.type = "desktop";
    }
    if (result.device.model === "iPad") result.device.type = "tablet";

    if (result.browser.webviewAppName !== "N/A" && result.browser.webviewAppName !== "Android System WebView" && !result.browser.isWebview) {
        result.browser.isWebview = true;
    }
    if (result.browser.isFacebookApp && !result.browser.isWebview) {
        result.browser.isWebview = true;
    }

    return result;
}