function dtime_nums(num) {
	var curDate = new Date().getTime() + (num + 1) * 24 * 3600 * 1000
	curDate = new Date(curDate)
	var curDay =
	curDate.getDate() >= 10
	? curDate.getDate()
	: '0' + curDate.getDate(),
	curMonth =
	curDate.getMonth() + 1 >= 10
	? curDate.getMonth() + 1
	: '0' + (curDate.getMonth() + 1),
	curYear = curDate.getFullYear()
	document.write(curYear + '.' + curMonth + '.' + curDay)
}
function dtime(language) { 
	var now = new Date(); 
	document.write(lang[language]["monthNames"][now.getMonth()]); 
} 
var lang = { 
	"es":{ 
		"monthNames":[ "enero" , "febrero" , "marzo" , "abril" , "mayo" , "junio" , "julio" , "agosto" , "septiembre" , "octubre" , "noviembre" , "diciembre" ] 
	}, 
	"ru":{ 
		"monthNames":[ "Январь" , "Февраль" , "Март" , "Апрель" , "Май" , "Июнь" , "Июль" , "Август" , "Сентябрь" , "Октябрь" , "Ноябрь" , "Декабрь" ] 
	}, 
	"it":{ 
		"monthNames":[ "gennaio" , "febbraio" , "marzo" , "aprile" , "maggio" , "giugno" , "luglio" , "agosto" , "settembre" , "ottobre" , "novembre" , "dicembre" ] 
	},
	"hu": {
		"monthNames":[ "január" , "február" , "március" , "április" , "május" , "június" , "július" , "augusztus" , "szeptember" , "október" , "november" , "december" ] 
	},
	"pl": {
		"monthNames":[ "Styczeń" , "Luty" , "Marzec" , "Kwiecień" , "Maj" , "Czerwiec" , "Lipiec" , "Sierpień" , "Wrzesień" , "Październik" , "Listopad" , "Grudzień" ] 
	},
	"cz": {
		"monthNames":[ "Leden" , "Únor" , "Březen" , "Duben" , "Květen" , "Červen" , "Červenec" , "Srpen" , "Září" , "Říjen" , "Listopad" , "Prosinec" ] 
	}
};
