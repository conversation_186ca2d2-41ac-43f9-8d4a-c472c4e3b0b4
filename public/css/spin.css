@-webkit-keyframes loading2 {
  0% {
    background-color: #2c3a86;
    opacity: 0;
  }
  100% {
    background-color: #fff;
    opacity: 1;
  }
}

@keyframes loading2 {
  0% {
    background-color: #2c3a86;
    opacity: 0;
  }
  100% {
    background-color: #fff;
    opacity: 1;
  }
}

.box-kk {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.order_block .main_form {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  background: url(../images/content/form_bg.webp) center top no-repeat;
  background-size: 100% 100%;
  padding: 50px 30px;
  text-align: center;
  margin: 0 auto;
  font-size: 18px;
  margin-bottom: 30px;
}

.order_block .price_red {
  font-weight: 600;
  color: #d74000;
}

.order_block img {
  vertical-align: middle;
  border-style: none;
}

.order_block .main_form h2 {
  font-size: 32px;
  line-height: 1.35;
  color: #000;
  font-weight: 600;
  margin-top: 10px;
  margin-bottom: 0;
  text-align: center;
  font-style: normal;
}

.order_block .sertif {
  color: #00f;
  font-size: 14px;
  font-style: italic;
}

.order_block .main_form p {
  padding: 0 0 0px;
  text-align: center;
  margin-bottom: 10px;
  line-height: 1.2em;
}

.order_block .main_form .price {
  margin-top: 0;
  padding: 0 20px;
  margin-bottom: 10px;
  text-align: center;
}

.order_block .blue-border {
  margin: 15px 20px 15px 20px;
  border: 2px solid #0299ff;
  padding: 10px !important;
  text-align: center !important;
  max-width: 700px;
  font-weight: 600;
  font-size: 14px;
  line-height: 1.35;
}

.order_block .main_form label {
  margin-top: 10px;
  font-size: 16px;
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
}

.order_block .main_form input {
  font-size: 18px;
  padding: 15px;
  border-radius: 3px;
  border: 2px solid #e9d7dc;
  max-width: 354px;
  width: 100%;
}

.order_block .main_form button,
.btn {
  margin: 20px;
  padding: 20px;
  color: #fff!important;
  font-weight: 700;
  text-decoration: none;
  border-radius: 5px;
  background-color: #d74000;
  background-position: 1.35 em 0.94 em;
  background-size: 1 em;
  background-repeat: no-repeat;
  -webkit-transition: all 0.15s ease-in-out;
  -o-transition: all 0.15s ease-in-out;
  transition: all 0.15s ease-in-out;
  border: none;
  cursor: pointer;
  font-size: 20px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

.order_block .main_form button,
.btn {
  -webkit-animation: glowing 900ms infinite both;
  animation: glowing 900ms infinite both;
}
.btn {
	display: inline-block;
	text-align: center;
	max-width: 400px;
}
.order_block .main_form button:hover,
.btn:hover {
	opacity: 0.8;
}
.order_block .additional,
.order_block .timelimit {
  font-size: 16px;
  line-height: 20px;
  text-align: center;
  padding: 0 20px;
}

.order_block .timelimit b {
  display: inline-block;
  background: url(http://localhost:8000/images/content/timerIcon2.webp) no-repeat;
  margin-top: -2px;
  padding-left: 14px;
  color: #d74000;
  vertical-align: middle;
  background-position: left center;
  font-size: 15px;
}

.attention {
  background: #fff;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  border-radius: 4px;
  width: 60%;
  margin: 32px auto;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding: 10px 14px;
  -webkit-box-shadow: 0 0 4px #555;
  box-shadow: 0 0 4px #555;
}

.attention img {
  width: 52px;
  height: 46px;
  margin-bottom: 10px;
}

.attention p {
  text-align: center;
  font-size: 12px;
  line-height: 16px;
}

.attention p strong {
  color: #d90000;
}

@media all and (max-width: 628px) {
  .features-wrapper .spin-wrapper {
    padding: 45px 0px;
  }
  .features-wrapper .spin-wrapper .wheel {
    width: 95%;
  }
  .features-wrapper .spin-wrapper .pop-up-heading {
    font-size: 33px !important;
  }
  .features-wrapper .spin-wrapper .pop-up-text {
    margin-bottom: 15px !important;
  }
  .features-wrapper .spin-wrapper .cursor-text {
    width: 30% !important;
    height: 30% !important;
    top: auto;
    left: auto;
    font-size: 14px;
    line-height: 39px;
  }
  .features-wrapper .spin-wrapper .cursor-wheel {
    width: 45%;
    top: -8.5%;
  }
  .features-wrapper .spin-wrapper .close-popup {
    position: absolute;
    width: 30px;
    height: 30px;
    background-size: 100%;
    top: 10px;
    border-radius: 50%;
    -webkit-box-shadow: 0 0 10px #fff;
    box-shadow: 0 0 10px #fff;
    right: 10px;
    cursor: pointer;
  }
  .features-wrapper .spin-wrapper .loading2 {
    width: 340px;
    height: 340px;
    display: none;
  }
  .features-wrapper .spin-wrapper .loading2 > div {
    width: 7px;
    height: 7px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(1) {
    top: 8.57px;
    left: 126.4px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(2) {
    top: 5.28px;
    left: 168.54px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(3) {
    top: 10.34px;
    left: 205.53px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(4) {
    top: 21.31px;
    left: 233.79px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(5) {
    top: 106.88px;
    left: 247.67px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(6) {
    top: 146.44px;
    left: 247.67px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(7) {
    top: 186px;
    left: 232.25px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(8) {
    top: 218.89px;
    left: 205.53px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(9) {
    top: 242.53px;
    left: 165.97px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(10) {
    top: 250.24px;
    left: 127.43px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(11) {
    top: 242px;
    left: 80.67px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(12) {
    top: 218.89px;
    left: 44.7px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(13) {
    top: 186.01px;
    left: 17.98px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(14) {
    top: 147.47px;
    left: 4.62px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(15) {
    top: 102.77px;
    left: 4.62px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(16) {
    top: 64.23px;
    left: 17.98px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(17) {
    top: 30.83px;
    left: 45.73px;
  }
  .features-wrapper .spin-wrapper .loading2 > div:nth-child(18) {
    top: 10.28px;
    left: 83.75px;
  }
}

@media (max-width: 627px) {
  .features-wrapper .spin-wrapper .cursor-wheel {
    top: -6.5%;
  }
  /* .features-wrapper .spin-wrapper .cursor-text {
          font-size: 10px;
      } */
}

@media (max-width: 480px) {
  .video-container {
    width: 340px;
    height: 190px;
  }
  .revolition
    .revolition-review
    .revolition-review__article
    strong:nth-of-type(2) {
    width: 100%;
  }
  .comments .other-comment a {
    padding: 10px 49px;
  }
  .content .content-comment {
    width: 100%;
  }
  .slider .slider-body {
    padding: 22px 10px;
    padding-bottom: 50px;
  }
  .comments .comments-items .answer {
    margin-left: 20px;
  }
  .revolition-header {
    flex-direction: column-reverse;
  }
  .revolition
    .revolition-review
    .revolition-review__photo
    .revolition-review__doctor {
    padding: 10px;
  }
  .features-wrapper .spin-wrapper .cursor-text {
    font-size: 10px;
  }
}

@media (max-width: 480px) {
  .conditions-wrapper {
    width: 95%;
  }
  .slider .slider-header img {
    width: 100%;
  }
  .order .order-product {
    height: 196px;
    top: 56px;
  }
  .order .order-btn {
    font-size: 15px;
  }
  .order .order-text p {
    font-size: 16px;
    line-height: 1.2;
  }
  .order .order-text strong {
    font-size: 34px;
  }
}

@media (max-width: 400px) {
  .features-wrapper .spin-wrapper .cursor-text {
    /* font-size: 9px; */
  }
  .order .order-product {
    height: 159px;
    top: 72px;
    left: 6px;
  }
}

.super-rotation {
  -webkit-animation-name: super-rotation;
  animation-name: super-rotation;
  -webkit-animation-duration: 7s;
  animation-duration: 7s;
  -webkit-animation-fill-mode: forwards;
  animation-fill-mode: forwards;
  -webkit-transition-timing-function: ease-in-out;
  -o-transition-timing-function: ease-in-out;
  transition-timing-function: ease-in-out;
}

@-webkit-keyframes super-rotation {
  70% {
    -webkit-transform: rotate(1440deg);
    transform: rotate(1440deg);
  }
  100% {
    -webkit-transform: rotate(1447deg);
    transform: rotate(1447deg);
  }
}

@keyframes super-rotation {
  70% {
    -webkit-transform: rotate(1440deg);
    transform: rotate(1440deg);
  }
  100% {
    -webkit-transform: rotate(1447deg);
    transform: rotate(1447deg);
  }
}

@-webkit-keyframes pop-up-appear {
  0% {
    -webkit-transform: translateY(-2000px);
    transform: translateY(-2000px);
  }
  30% {
    -webkit-transform: translateY(100px);
    transform: translateY(100px);
  }
  100% {
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
}

@keyframes pop-up-appear {
  0% {
    -webkit-transform: translateY(-2000px);
    transform: translateY(-2000px);
  }
  30% {
    -webkit-transform: translateY(100px);
    transform: translateY(100px);
  }
  100% {
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
  }
}

@-webkit-keyframes pop-up-appear-before {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@keyframes pop-up-appear-before {
  0% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

@-webkit-keyframes pop-up-appear-after {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes pop-up-appear-after {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-webkit-keyframes scale {
  0% {
    -webkit-transform: scale(100%);
    transform: scale(100%);
  }
  100% {
    -webkit-transform: scale(110%);
    transform: scale(110%);
  }
}

@keyframes scale {
  0% {
    -webkit-transform: scale(100%);
    transform: scale(100%);
  }
  100% {
    -webkit-transform: scale(110%);
    transform: scale(110%);
  }
}

@-webkit-keyframes rotation-min {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@keyframes rotation-min {
  0% {
    -webkit-transform: rotate(0);
    transform: rotate(0);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}

@-webkit-keyframes glowing {
  0% {
    -webkit-box-shadow: 0 0 4px #d90000;
    box-shadow: 0 0 2px #d90000;
  }
  50% {
    -webkit-box-shadow: 0 0 12px #d90000;
    box-shadow: 0 0 12px #d90000;
  }
  100% {
    -webkit-box-shadow: 0 0 4px #d90000;
    box-shadow: 0 0 8px #d90000;
  }
}

@keyframes glowing {
  0% {
    -webkit-box-shadow: 0 0 4px #d90000;
    box-shadow: 0 0 2px #d90000;
  }
  50% {
    -webkit-box-shadow: 0 0 12px #d90000;
    box-shadow: 0 0 12px #d90000;
  }
  100% {
    -webkit-box-shadow: 0 0 4px #d90000;
    box-shadow: 0 0 8px #d90000;
  }
}

.spin-result-wrapper {
  display: none;
  padding: 0 10px;
  width: 100%;
  top: 0;
  z-index: 2;
  left: 0;
  height: 100%;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.6);
}

.spin-result-wrapper .pop-up-window {
  position: relative;
  max-width: 400px;
  right: 0;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  transform: translateY(-50%);
  margin: 0 auto;
  background: #fff;
  padding: 70px 10px 20px;
  border-radius: 10px;
  -webkit-animation: 0.7s ease 0s normal none 1 running pop-up-appear;
  animation: 0.7s ease 0s normal none 1 running pop-up-appear;
  background-image: url(../images/content/bg-prise.webp);
  background-position: center;
  background-size: cover;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -ms-flex-line-pack: center;
  align-content: center;
}

.spin-result-wrapper .pop-up-window::after,
.spin-result-wrapper .pop-up-window::before {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
}

.spin-result-wrapper .pop-up-window::before {
  width: 110px;
  height: 110px;
  top: -55px;
  background: -webkit-gradient(
    linear,
    left top,
    left bottom,
    color-stop(21.15%, #2aa6eb),
    to(#006aa5)
  );
  background: -o-linear-gradient(top, #2aa6eb 21.15%, #006aa5 100%);
  background: linear-gradient(180deg, #2aa6eb 21.15%, #006aa5 100%);
  border-radius: 50%;
  -webkit-animation: 0.5s ease 0.6s normal backwards 1 running
    pop-up-appear-before;
  animation: 0.5s ease 0.6s normal backwards 1 running pop-up-appear-before;
}

.spin-result-wrapper .pop-up-window::after {
  content: url(../images/content/med.svg);
  width: 50px;
  height: 20px;
  top: -20px;
  -o-border-image: none;
  border-image: none;
  -webkit-transform: translate(-20px, -16px);
  -ms-transform: translate(-20px, -16px);
  transform: translate(-20px, -16px);
  -webkit-animation: 0.5s ease 0.6s normal backwards 1 running
    pop-up-appear-after;
  animation: 0.5s ease 0.6s normal backwards 1 running pop-up-appear-after;
}

.spin-result-wrapper .close-popup,
.spin-result-wrapper .cursor-text {
  cursor: pointer;
  position: absolute;
}

.spin-result-wrapper .close-popup {
  width: 30px;
  height: 30px;
  background-image: url(http://localhost:8000/fonts/cross.svg);
  background-size: 100%;
  top: 10px;
  border-radius: 50%;
  -webkit-box-shadow: 0 0 10px #fff;
  box-shadow: 0 0 10px #fff;
  right: 10px;
}

.spin-result-wrapper .pop-up-heading {
  display: block;
  font-size: 40px;
  margin-bottom: 20px;
  text-align: center;
}

.spin-result-wrapper p.pop-up-text {
  margin: 20px auto 25px;
  font-size: 24px;
  line-height: 30px;
  text-align: center;
  text-indent: 0;
  color: #414141;
  font-weight: 400;
}

.spin-result-wrapper .pop-up-button {
  text-transform: uppercase;
  padding: 10px 20%;
  font-size: 20px;
  border-radius: 5px;
  background-color: #2aa6eb;
  border: none;
  cursor: pointer;
  outline: 0;
  border-radius: 50px;
  text-align: center;
  width: 50%;
  -ms-flex-item-align: center;
  align-self: center;
  text-decoration: none;
  color: #fff!important;
}

@media (max-width: 639px) {
  .gift__background {
    position: relative;
    left: 0;
    top: 0;
    margin-bottom: 10px;
  }
  .container {
    overflow: hidden;
  }
  .order_block .main_form input {
    width: 85%;
  }
}

@media (max-width: 480px) {
  .gift__text {
    font-size: 14px;
  }
  .gift__background {
    padding: 10px 15px;
  }
  .container {
    margin: 0 auto;
    border-radius: 0;
  }
}

@media (max-width: 420px) {
  .animation-link {
    /* font-size: 16px; */
    font-size: 21px;
  }
  .features-wrapper .spin-wrapper p:nth-of-type(2) {
    padding: 0 15px;
  }
}

.link__reset {
  color: inherit;
  text-decoration: none;
}

.product__section {
  position: relative;
  margin: 22px 0;
  text-align: center;
}
.product__image {
}
.product__link {
}

.animation-link {
  animation: animation-link 2s;
  transition: 0.2s;
  animation-iteration-count: infinite;
  font-family: 'Roboto';
  font-style: normal;
  font-weight: bold;
  /* font-size: 16px; */
  font-size: 21px;
  line-height: 23px;
  text-align: center;
  margin: 10px 0;

  text-decoration-line: underline;

  color: #ff0000 !important;
}

.link-box {
  margin-left: 6px;
}

a .animation-link:hover {
  text-decoration: none;
}
.link-box .animation-link-item {
  font-weight: bold;
}
.link-box .animation-link-item:nth-child(1) {
  animation: animation-link1 2s ease;
  transition: 0.2s;
  animation-iteration-count: infinite;
}
.link-box .animation-link-item:nth-child(2) {
  animation: animation-link2 2s ease;
  transition: 0.2s;
  animation-iteration-count: infinite;
}
.link-box .animation-link-item:nth-child(3) {
  animation: animation-link3 2s ease;
  transition: 0.2s;
  animation-iteration-count: infinite;
}
@keyframes animation-link {
  from {
    color: #ca5218;
  }

  to {
    color: #f30000;
  }
}
@keyframes animation-link1 {
  from {
    display: none;
    opacity: 0;
    transform: scale(0);
    -webkit-transform: scale(0);
  }
  25% {
    display: block;
    opacity: 1;
    transform: scale(1);
    -webkit-transform: scale(1);
  }
  50% {
    display: block;
    opacity: 1;
    transform: scale(1);
    -webkit-transform: scale(1);
  }
  75% {
    display: block;
    opacity: 1;
    transform: scale(1);
    -webkit-transform: scale(1);
  }
  to {
    display: block;
    opacity: 1;
    transform: scale(1);
    -webkit-transform: scale(1);
  }
}
@keyframes animation-link2 {
  from {
    display: none;
    opacity: 0;
    transform: scale(0);
    -webkit-transform: scale(0);
  }
  25% {
    display: none;
    opacity: 0;
    transform: scale(0);
    -webkit-transform: scale(0);
  }
  50% {
    display: block;
    opacity: 1;
    transform: scale(1);
    -webkit-transform: scale(1);
  }
  75% {
    display: block;
    opacity: 1;
    transform: scale(1);
    -webkit-transform: scale(1);
  }
  to {
    display: block;
    opacity: 1;
    transform: scale(1);
    -webkit-transform: scale(1);
  }
}
@keyframes animation-link3 {
  from {
    display: none;
    opacity: 0;
    transform: scale(0);
    -webkit-transform: scale(0);
  }
  50% {
    display: none;
    opacity: 0;
    transform: scale(0);
    -webkit-transform: scale(0);
  }
  75% {
    display: block;
    opacity: 1;
    transform: scale(1);
    -webkit-transform: scale(1);
  }
  to {
    display: block;
    opacity: 1;
    transform: scale(1);
    -webkit-transform: scale(1);
  }
}
.garanta__wrap {
  position: relative;
  text-align: center;
  margin: 30px 0;
}

.button-gradient {
  padding: 17px 0;
  max-width: 269px;
  width: 100%;
  margin: 20px auto;
  display: block;
  background: linear-gradient(
    149deg,
    rgba(24, 187, 156, 1) 0%,
    rgba(106, 57, 175, 1) 42%,
    rgba(187, 24, 148, 1) 72%,
    rgba(115, 53, 134, 1) 100%
  );
  animation: gradient 5s infinite linear;
  background-size: 400%;
  box-shadow: 2px 3px 5px rgba(0, 0, 0, 0.25);
  border-radius: 10px;
  font-weight: bold;
  font-size: 18px;
  line-height: 21px;
  letter-spacing: 0.035em;
  text-transform: uppercase;
  color: #fff;
}

@keyframes gradient {
  0% {
    background-position: 80% 0%;
  }
  50% {
    background-position: 20% 100%;
  }
  100% {
    background-position: 80% 0%;
  }
}


.order-info {
  text-align: center;
}

.order-info__title {
  font-size: 26px;
  color: #d74000;
}

.order-info__date {
  text-decoration: underline;
}

.order-info__count {
  display: block;
  background: #d74000;
  padding: 10px;
  margin: 12px auto;
  margin-bottom: 25px;
  width: 178px;
  color: #fff;
  text-shadow: 0 -1px 0 #000000, 0 -1px 0 #000000, 0 1px 0 #000000,
    0 1px 0 #000000, -1px 0 0 #000000, 1px 0 0 #000000, -1px 0 0 #000000,
    1px 0 0 #000000, -1px -1px 0 #000000, 1px -1px 0 #000000, -1px 1px 0 #000000,
    1px 1px 0 #000000, -1px -1px 0 #000000, 1px -1px 0 #000000,
    -1px 1px 0 #000000, 1px 1px 0 #000000;
  font-size: 32px;
  border-radius: 5px;
  animation-name: button;
  animation-duration: 1s;
  animation-iteration-count: infinite;
}

@keyframes button {
  0% {
    box-shadow: 0px 0px 10px #f20000;
  }
  60% {
    box-shadow: 0px 0px 3px rgba(242, 0, 0, 0.8);
  }
  100% {
    box-shadow: 0px 0px 10px #f20000;
  }
}

.article__text {
  padding-top: 20px;
}

.article__text b,
.article__text strong {
  font-weight: bold;
}

.order-info__subtitle {
  font-size: 24px;
  margin: 22px auto;
}

.order-info__subtitle-price {
  font-weight: 600;
  color: #d74000;
}

.order-info__count-val {
  font-size: 52px;
  line-height: 1.35;
}
