{"name": "estree-walker", "description": "Traverse an ESTree-compliant AST", "version": "0.6.1", "author": "<PERSON>", "license": "MIT", "typings": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/<PERSON>-<PERSON>/estree-walker"}, "main": "dist/estree-walker.umd.js", "module": "src/estree-walker.js", "scripts": {"prepublishOnly": "npm test", "build": "rollup -c", "test": "mocha test/test.js", "pretest": "npm run build"}, "devDependencies": {"mocha": "^5.2.0", "rollup": "^0.67.3"}, "files": ["src", "dist", "index.d.ts", "README.md"]}