$('a').click(function (e) {
    e.preventDefault();
    var top;
    top = $('.toscroll').offset().top;
    $('body,html').animate({
        scrollTop: top
    }, 800);
});

const swiper = new Swiper('.swiper', {
    // Optional parameters
    direction: 'horizontal',
    loop: false,
    autoHeight: false,
    // effect: "flip",
    autoplay: {
        delay: 14000,
    },

    // If we need pagination
    pagination: {
        el: '.swiper-pagination',
        clickable: true,
    },


});

const myswiper = new Swiper('.swiper2', {
    // Optional parameters
    direction: 'horizontal',
    loop: false,
    autoHeight: false,
    effect: "cube",
    autoplay: {
        delay: 14000,
    },

    // If we need pagination
    pagination: {
        el: '.swiper-pagination',
        clickable: true,
    },


});

localStorage.getItem("prodLeft")
    ? (document.querySelector(".order-info__count-val").innerHTML =
        localStorage.getItem("prodLeft"))
    : (document.querySelector(".order-info__count-val").innerHTML = 23);
