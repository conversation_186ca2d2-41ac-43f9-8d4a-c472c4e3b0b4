.mt-20 {
    margin-top: 20px;
}
.mt-35 {
    margin-top: 35px;
}
.mb-20 {
    margin-bottom: 20px;
}
.mb-25 {
    margin-bottom: 25px!important;
}
.mb-30 {
    margin-bottom: 30px;
}
.mb-35 {
    margin-bottom: 35px!important;
}
.cikk-torzs h2 {
    text-align: center;
    max-width: 100%;
}
.main-content {
    padding-top: 10px;
}
.b-tags {
    margin: 0 10px;
    margin-bottom: 15px;
}
.article-info {
    margin: 0 10px;
    margin-bottom: 15px;
}
.center-img {
    text-align: center;
    margin-bottom: 15px;
}
.center-img img {
    max-width: 100%;
}

.main-content > p {
    padding: 0 20px;
    margin-bottom: 15px;
    font-size: 18px;
    line-height: 1.4em;
}
.main-content > p span {
    font-weight: 700;
}
.main-content > h2 {
    padding: 0 20px;
    margin-bottom: 25px;
}
.img-descripted img {
    margin-bottom: 20px;
}
.img-descripted p {
    display: block;
    text-align: center;
    margin: 0 auto;
    max-width: 610px;
    font-size: 14px;
    line-height: 1.4em;
    font-style: italic;
    font-weight: 700;
    padding: 0 10px;
}
.img-descripted {
    margin-bottom: 25px;
}

.violet {
    color: #0e0097;
}

.cikk-torzs .excl {
    margin: 30px 16px 0;
    background: #f74447;
    color: #fff;
    font-size: 18px;
    line-height: 23px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-left: 60px;
    padding-right: 60px;
    min-height: 80px;
    border-radius: 0 100px 100px 0;
    overflow: hidden;
    position: relative;
    margin-bottom: 25px;
	padding-top: 0!important;
}

.cikk-torzs .excl_green {
    background: #457f10;
}

.cikk-torzs .excl-blue {
    background: #2145ac;
}
.cikk-torzs .excl p {
    margin: 15px 0;
    font-weight: bold;
}
.cikk-torzs .excl span.red {
    color: #f1d37a !important;
}
.cikk-torzs .excl a {
    color: #fff !important;
    border-bottom: 1px dotted #fff;
}
.cikk-torzs .excl::before {
    content: "";
    display: block;
    width: 18px;
    height: 80px;
    left: 22px;
    top: -10px;
    position: absolute;
    background: url(../images/content/excl.png) center center no-repeat;
    background-size: contain;
}


/* .cikk-torzs .quotes {
    margin: 0px 0 0;
}
.cikk-torzs .quotes__content {
    position: relative;
    padding: 35px 45px 35px 25px;
    background: #f8f8f8;
}

.cikk-torzs .quotes ul {
    list-style-type: none;
}

.cikk-torzs .quotes li {
    padding-left: 60px;
    display: block;
    position: relative;
    margin-bottom: 25px;
}

.cikk-torzs .quotes li span {
    font-weight: 700;
}
.cikk-torzs .quotes li:last-of-type {
    margin-bottom: 0;
}

.cikk-torzs .quotes li::before {
    content: "";
    display: block;
    width: 30px;
    height: 30px;
    background: url(../images/content/check_green.png) center center no-repeat;
    background-size: contain;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
}

.cikk-torzs .quotes li::after {
    content: "";
    display: block;
    position: absolute;
    background: #e1e1e4;
    width: 1px;
    height: 85%;
    left: 40px;
    top: 50%;
    transform: translateY(-50%);
}


.cikk-torzs .quotes h2 {
    margin: 0;
}
.cikk-torzs .quotes p {
    margin: 0;
}
.cikk-torzs .quotes.quotes-butterfly h2,
.cikk-torzs .quotes.quotes-butterfly h3,
.cikk-torzs .quotes.quotes-green h2,
.cikk-torzs .quotes.quotes-red h2 {
    margin-left: 49px;
    padding-bottom: 10px;
}

.cikk-torzs .quotes.quotes-green li::before {
    content: "";
    display: block;
    width: 31px;
    height: 31px;
    background: url(../images/content/check_big.png) center center no-repeat;
		background-size: contain;
		position: absolute;
		left: -15px;
	top: -5px;
    transform: translateY(0);
    z-index: 101;
}
.cikk-torzs .quotes.quotes-red li::before {
    content: "";
    display: block;
    width: 19px;
    height: 79px;
    background: url(../images/content/symptom_big.png) center center no-repeat;
    background-size: contain;
    position: absolute;
    left: -8px;
    top: 0;
    transform: translateY(0);
    z-index: 101;
}

.cikk-torzs .quotes.quotes-butterfly li::before {
    content: "";
    display: block;
    width: 100px;
    height: 100px;
    background: url(../images/content/butterfly.png) center center no-repeat;
    background-size: contain;
    position: absolute;
    left: -70px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
}
.cikk-torzs .quotes.quotes-butterfly .yellow-bg {
    font-size: 18px;
} */

.videos {
    margin: 0 20px;
    margin-bottom: 15px;
}
.videos h3 {
    text-align: center;
    margin-bottom: 5px;
}

.cikk-torzs .conditions {
    margin: 0 16px 16px;
    padding-top: 35px;
    background: #f3f3f4;
    font-size: 16px;
    line-height: 1.5;
}
.cikk-torzs .conditions .badge {
    display: table;
    width: 130px;
    margin: -100px auto 30px;
    border-radius: 50%;
}
.cikk-torzs .conditions h3,
.cikk-torzs .conditions p {
    text-align: center;
    margin: 0 30px;
    margin-bottom: 40px;
    position: relative;
    font-size: 18px;
    max-width: 100%;
}
.cikk-torzs .conditions h3 {
    color: #d74000;
}
.cikk-torzs .conditions h3::after {
    content: "";
    display: block;
    width: 90px;
    height: 2px;
    background: #d74000;
    bottom: -20px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}
.cikk-torzs .conditions ul {
    list-style-type: none;
    padding-right: 25px;
    padding-bottom: 30px !important;
    padding-left: 25px;
}
.cikk-torzs .conditions li {
    padding-left: 35px;
    display: block;
    margin-bottom: 25px;
    position: relative;
}
.cikk-torzs .conditions li::before {
  content: '';
  display: block;
  width: 20px;
  height: 20px;
  position: absolute;
  left: 0;
  top: 5px;
  background: url(../images/content/check_red.png) center center no-repeat;
  background-size: contain;
}
.cikk-torzs .conditions li:last-of-type {
    margin-bottom: 0;
}
.cikk-torzs .conditions li span {
    font-weight: 700;
}

.order-info {
    text-align: center;
}
.order-info__title {
    font-size: 22px;
    color: #d74000;
}
.order-info__date {
    text-decoration: underline;
}
.order-info__count {
    display: block;
    background: #d74000;
    padding: 10px;
    margin: 12px auto;
    margin-bottom: 25px;
    width: 200px;
    color: #fff;
    text-shadow: 0 -1px 0 #000000, 0 -1px 0 #000000, 0 1px 0 #000000, 0 1px 0 #000000, -1px 0 0 #000000, 1px 0 0 #000000, -1px 0 0 #000000, 1px 0 0 #000000, -1px -1px 0 #000000, 1px -1px 0 #000000, -1px 1px 0 #000000, 1px 1px 0 #000000,
	-1px -1px 0 #000000, 1px -1px 0 #000000, -1px 1px 0 #000000, 1px 1px 0 #000000;
    font-size: 32px;
    border-radius: 5px;
    animation-name: button;
    animation-duration: 1s;
    animation-iteration-count: infinite;
}

@keyframes button {
    0% {
        box-shadow: 0px 0px 10px #f20000;
	}
    60% {
        box-shadow: 0px 0px 3px rgba(242, 0, 0, 0.8);
	}
    100% {
        box-shadow: 0px 0px 10px #f20000;
	}
}

.order-info__count-val {
    font-size: 52px;
    line-height: 1.4em;
}
.form__wrapper {
    margin: 0 auto;
}
@media (min-width: 1199px) {
    .form__wrapper {
        margin: 0 0 2rem var(--grid-8-1-column-content-gap);
	}
}
.timelimit,
.additional {
    text-align: center;
    padding: 0 20px;
}
.timelimit b {
    display: inline-block;
    background: url(../images/content/timerIcon2.png) no-repeat;
    padding-left: 14px;
    color: #d74000;
    vertical-align: top;
    background-position-y: 3px;
}

.comments-block .b-tags {
    margin: 0 auto;
    margin-bottom: 15px;
}
.comments__add {
    background: #e5ecf9;
    padding: 25px;
    padding-left: 105px;
    margin-bottom: 20px;
    position: relative;
}
.comments__add input {
    display: block;
    width: 100%;
    background: #fff;
    padding: 10px 30px;
    border: 1px solid #a8a8a8;
    border-radius: 2px;
    outline: none;
    font-size: 14px;
    line-height: 1.4em;
}
.input__container {
    position: relative;
}

.timer_wrap {
    display: inline;
    background: url(../images/content/timerIcon2.png) no-repeat left center;
    padding-left: 14px;
    color: #d74000;
    vertical-align: top;
    font-size: 14px;
    line-height: 24px;
    font-weight: bold;
    text-align: center;
}
.time_remains {
    font-size: 1.1em;
    color: #d74000;
    font-weight: 700;
}

.yellow-bg {
    background: #fcd72b;
    color: #000;
}
.green-bg {
    background: #76d6a9;
    color: #000;
}
.violet-bg {
    background: #d7ace9;
    color: #000;
}

.prod_wrapper__listy {
    text-align: left;
    padding: 20px;
    background: #f3f3f4;
    box-sizing: border-box;
    margin-bottom: 25px;
    margin-top: 100px;
}
.prod_wrapper__listy .badge {
    display: table;
    width: 130px;
    margin: -100px auto 10px;
}
.prod_wrapper__listy h2 {
    text-align: center;
}

.prod_wrapper__listy p {
    margin: 0 0 8px;
}
blockquote {
    line-height: 26px;
    font-size: 1.2em;
}
.blue-bg {
    background: #fef2c3;
}
.prod_wrapper__list {
    margin-top: 20px;
}
.prod_wrapper__list p {
    margin: 10px 0 5px 0 !important;
    display: flex;
    gap: 10px;
}
.prod_wrapper__list p b {
    font-size: 20px;
    display: block;
    flex: 0 0 40px;
    padding-left: 10px;
}
.red {
    color: #d74000 !important;
}

.swiper {
    width: 300px;
    height: auto;
    /* border: 1px solid black; */
    padding-bottom: 20px;
    margin-bottom: 20px;
    position: relative;
}
.swiper1 {
    text-align: center;
}
.swiper2 {
    width: 100%;
    max-width: 775px;
    height: auto;
    padding-bottom: 15px;
    margin: 0 auto 2rem;
    position: relative;
}
@media (min-width: 1199px) {
    .swiper2 {
        margin: 0 0 2rem var(--grid-8-1-column-content-gap);
        width: 100%;
	}
}
.cikk-torzs .slider-1 {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.cikk-torzs .slider-2 {
    background-color: #fff;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.2);
}

.cikk-torzs .slider-1__text {
    font-style: normal;
    font-weight: 400;
    font-size: 15px;
    line-height: 16px;
    text-align: center;
	
    color: #000000;
	
    padding: 33px 19px 9px;
    box-shadow: 2px 1px 2px rgba(51, 26, 26, 0.15);
    position: relative;
    top: -20px;
}
.cikk-torzs .slider-1__text:after {
    content: "";
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    position: absolute;
    background: linear-gradient(90deg, #dd7000 0%, #f2b200 48%, #f3ad01 100%);
    opacity: 0.3;
}
.cikk-torzs .slider-1__img {
    z-index: 2;
    /* box-shadow: (1px 1px 3px rgba(0, 0, 0, 0.25)); */
    box-shadow: 1px 1px 3px rgba(0, 0, 0, 0.25);
}

.cikk-torzs .slider-1__text__text {
    z-index: 2;
    position: relative;
}

.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal,
.swiper-pagination-custom,
.swiper-pagination-fraction {
    margin-top: 0px;
}

.swiper-pagination-bullet {
    width: 17px;
    height: 17px;
	
    background: #ffffff;
    border: 8px solid #0e0097;
}

.swiper-pagination-bullet-active {
    background: linear-gradient(265.51deg, #b61814 0%, #d8b022 100%);
    box-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
}

.new-pag > .swiper-pagination-bullet {
    background: transparent;
    width: 42px;
    height: 42px;
    border-radius: 0;
    background-image: url(../images/content/facial-mask-with-flower-in-spa.svg);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    border: none;
    opacity: 0.3;
    outline: none;
}
.new-pag > .swiper-pagination-bullet-active {
    border: none;
    box-shadow: none;
    background-size: cover;
    opacity: 1;
    outline: none;
}

.cikk-torzs .slider-top {
    display: flex;
    align-items: center;
    height: 50px;
    background-color: #fff;
    padding: 0 15px;
}
.cikk-torzs .slider-top__img {
    width: 34px;
    height: 34px;
    border-radius: 50%;
    background: #ffffff;
    border: 1px solid #dce2e9;
    padding: 2px;
    margin-right: 11px;
    background-size: cover;
}
.cikk-torzs .slider-top__name {
    font-style: normal;
    font-weight: 700;
    font-size: 15px;
    line-height: 16px;
	
    color: #131212;
}
.cikk-torzs .slider-top__doted {
    margin-left: auto;
}
.cikk-torzs .slider-image {
}
.cikk-torzs .slider-nav {
    display: flex;
    padding: 10px 10px 0px 10px;
    background-color: #fff;
    position: relative;
    top: -5px;
}
.cikk-torzs .slider-nav img {
    max-width: 24px;
    height: 24px;
    margin-left: 5px;
}
.cikk-torzs .slider-bottom {
    padding: 0 15px;
    background-color: #fff;
}
.cikk-torzs .slider-bottom__text2 {
    font-style: normal;
    font-weight: 400 !important;
    margin-bottom: 16px;
    padding-bottom: 20px;
    color: #131212 !important;
}

.cikk-torzs .effects {
    background: linear-gradient(117.36deg, #3262be 2.35%, #21417d 99.66%);
    color: #fff;
    margin: 30px 16px 0px;
	padding: 0!important;
}
.cikk-torzs .effects__container {
    background: url(../images/content/check_bg.png) left center no-repeat;
    background-size: 215px;
    padding: 25px 100px;
}
.cikk-torzs .effects h3 {
    font-size: 1.5em;
    line-height: 1.2em;
    position: relative;
    margin-bottom: 30px;
    text-align: center;
    max-width: 100%;
    color: #fff;
}
.cikk-torzs .effects h3::after {
    content: "";
    display: block;
    width: 100px;
    height: 2px;
    background: #fff;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    bottom: -18px;
}
.cikk-torzs .effects li {
    font-size: 18px;
    min-height: 35px;
    padding-left: 30px;
    position: relative;
    margin-bottom: 0;
    padding-bottom: 0;
}
.cikk-torzs .effects li::before {
    content: '';
    position: absolute;
    display: block;
    width: 18px;
    height: 15px;
    background: url(../images/content/check.png) center center no-repeat;
    background-size: contain;
    left: 0;
    top: 4px;
}

.cikk-torzs .effects li p {
    margin-bottom: 0;
    font-weight: 400;
}
.sidegutterleft .swiper-wrapper {
    flex-wrap: wrap;
}
.sidegutterleft .swiper-wrapper .swiper-slide {
    height: auto;
}

div.blur {
    position: relative;
    background: #eee;
    padding: 10px;
    cursor: pointer;
}

div.blur img {
    display: block;
    margin: 0 auto;
    -webkit-filter: blur(15px);
    -moz-filter: blur(15px);
    -o-filter: blur(15px);
    -ms-filter: blur(15px);
    filter: blur(15px);
    width: auto;
    max-width: 100%;
}

.blur-p {
    position: absolute;
    top: calc(50% - 12px);
    left: 50%;
    transform: translateX(-50%);
    font-size: 24px !important;
    text-transform: uppercase;
    color: #fff;
    text-shadow: 1px 1px 1px #000;
    text-align: center;
}
.swiper1 .swiper-cube-shadow {
    display: none !important;
}
.swiper1 {
    margin-left: auto !important;
    margin-right: auto !important;
}
@media only screen and (min-width: 1024px) {
    .swiper1 img {
        height: 332px;
	}
    .swiper1 .article__media-caption {
        margin-bottom: 0;
        padding-bottom: 8px;
	}
}

.swiper-backface-hidden .swiper-slide {
    max-width: 100%;
}
.discussion-container a span,
.article-body-text a span {
    cursor: pointer;
    position: relative;
    text-decoration: underline;
    z-index: 0;
    font-weight: 700;
}
.discussion-container a:hover span,
.article-body-text a:hover span {
    text-decoration: none;
}
.discussion-container a span::before,
.article-body-text a span::before {
    display: block;
    content: "";
    background-color: rgba(0, 159, 255, 0.4);
    position: absolute;
    left: 0;
    bottom: 3px;
    width: 100%;
    height: 8px;
    z-index: -1;
    -webkit-transition: all 0.3s ease-in-out;
    -o-transition: all 0.3s ease-in-out;
    transition: all 0.3s ease-in-out;
}
.discussion-container a:hover span::before,
.article-body-text a:hover span::before {
    bottom: 0;
    height: 100%;
}
/* 
.cikk-torzs .list {
    padding-left: 23px;
    margin-bottom: 30px;
}
.cikk-torzs .list ul {
    list-style-type: none;
    margin-left: 1.25rem;
}
.cikk-torzs .list li {
    display: block;
    margin-bottom: 15px;
    background: linear-gradient(95.88deg, #c2e2cc 33.37%, transparent 86.23%);
    padding: 3px 12px;
    padding-right: 50px;
    border-left: 2px solid #189240;
    position: relative;
    line-height: 1.4em;
    margin-left: 0;
}
.cikk-torzs .list li:last-of-type {
    margin-bottom: 0;
}
.cikk-torzs .list li::before {
    content: "";
    display: block;
    width: 20px;
    height: 20px;
    position: absolute;
    left: -30px;
    top: 0px;
    background: url(../images/content/check_green.png) center center no-repeat;
    background-size: contain;
    border-radius: 0 !important;
    margin-top: 0;
}
.cikk-torzs .list.blue-list li {
    background: linear-gradient(95.88deg, #d9e4ff 33.37%, #f6f6f6 86.23%);
    border-left: 2px solid #2b3053;
}
.cikk-torzs .list.blue-list li::before {
    background: url(../images/content/check_violet.png) center center no-repeat;
    background-size: contain;
} */
.cikk-torzs .warning {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    border: 0.3px solid #000530;
    border-radius: 2px;
    background: linear-gradient(142.75deg, #fce376 -11.7%, #fce479 11.09%, #fde681 25.06%, #feeb8f 36.64%, #fff0a0 45.29%, #bc6d00 193.23%) top center;
    width: 100%;
    max-width: 300px;
    padding: 12px 14px;
    margin: 0 0 2rem var(--grid-8-1-column-content-gap);
}
.cikk-torzs .warning > img {
    width: 52px;
    height: 46px;
}
.cikk-torzs .warning-text {
    text-align: center;
    margin: 0 0 5px;
    line-height: 1.2em;
}
.cikk-torzs .warning-text > strong {
    display: block;
    margin-bottom: 5px;
    font-weight: 700;
}
.delivery {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0px 0;
    text-align: left;
}
.delivery div {
    flex: 0 0 50px;
}

@media (max-width: 1023px) {
    .cikk-torzs .order_block .main_form {
        padding: 30px 15px;
	}
    .cikk-torzs .main_form h2 {
        font-size: 26px;
	}
    .cikk-torzs .excl {
        margin: 16px 0 0 0;
        margin-bottom: 25px;
        font-size: 16px;
        line-height: 1.4em;
        padding-right: 35px;
		padding-left: 76px;
	}
	.cikk-torzs .excl::before {
		left: 30px;
	}
    .cikk-torzs .quotes {
        margin: 0px 0 0;
        margin-bottom: 30px;
	}
    .cikk-torzs .quotes__content {
        padding: 50px 5px 10px;
	}
    .cikk-torzs .quotes li {
        font-size: 16px;
        line-height: 1.4em;
	}
    .cikk-torzs .effects__container {
        padding: 15px;
	}
    .cikk-torzs .answer:before {
        top: 10px;
	}
	.cikk-torzs .effects, .cikk-torzs .conditions {
		margin: 30px 0 10px;
	}

    .question::before
    .asnwer::before {
        margin-left: 20px;
    }
}

.comments-block {
    margin-top: 40px;
}
.comments-block .b-tags > span {
    font-size: 28px;
    margin-bottom: 10px;
    padding: 0 5px;
    line-height: 1.2;
}
.comments-block .b-tags .b-social {
    margin-top: 7px;
}
.comments-block .b-tags .b-social span {
    color: #bcbcbc;
    font-size: 11px;
    display: inline-block;
    vertical-align: middle;
}
.comments-block .b-tags .b-social a {
    display: inline-block;
    vertical-align: middle;
}
.comment-item {
    margin-bottom: 15px;
}
.comment-body,
.comment-img {
    display: table-cell;
    vertical-align: top;
}
.comment-img {
    padding-right: 15px;
}
.comment-author {
    color: #000;
    font-weight: 700;
    font-size: 16px;
    text-decoration: none;
    margin-right: 15px;
    margin-bottom: 5px;
    display: inline-block;
}
.comment-date {
    font-size: 13px;
    color: #adb2bf;
    margin-right: 10px;
}
.comment-like {
    font-size: 13px;
    color: #adb2bf;
}
.comment-like svg {
    display: inline-block;
    position: relative;
    top: -2px;
    vertical-align: middle;
    margin-left: 5px;
}
.comment-body img {
    display: inline-block;
    vertical-align: top;
    margin-bottom: 10px;
}

.comments-block .b-tags {
    margin: 0 auto;
    margin-bottom: 15px;
}
.comments__add {
    background: #e5ecf9;
    padding: 25px;
    padding-left: 105px;
    margin-bottom: 20px;
    position: relative;
}
.comments__add input {
    display: block;
    width: 100%;
    background: #fff;
    padding: 10px 30px;
    border: 1px solid #a8a8a8;
    border-radius: 2px;
    outline: none;
    font-size: 14px;
    line-height: 19px;
}
.input__container {
    position: relative;
}
.comments__submit {
    display: block;
    position: absolute;
    width: 20px;
    height: 20px;
    background: url(../images/content/add.png) center center no-repeat;
    background-size: contain;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
}
.comments__avatar {
    display: block;
    position: absolute;
    width: 50px;
    height: 50px;
    background: url(../images/content/user.png) center center no-repeat;
    background-size: contain;
    left: 25px;
    top: 50%;
    transform: translateY(-50%);
}
.comment-body p {
    font-size: 16px;
    line-height: 22px;
    margin-bottom: 5px;
}
.comment-body img {
    margin-bottom: 0;
}
.comment-img img {
    max-width: 70px;
}
.comment-body p span {
    font-weight: 700;
}
.comment-item_subcomment {
    margin-left: 85px;
}
.cikk-torzs a {
    text-decoration: none;
}
.cikk-torzs a span {
    cursor: pointer;
    position: relative;
    z-index: 0 !important;
    font-weight: 700;
}
.cikk-torzs a:hover {
    text-decoration: none;
}

.cikk-torzs a:hover span::before {
    bottom: 0;
    height: 100%;
}
.green {
    color: #744256;
}
img {
	max-width: 100%;
}
.text-center {
	text-align: center;
}

.txt {
    margin-top: 10px;
    margin-bottom: 10px;
}

.txt--border {
    padding-left: 10px;
    border-left: 9px solid #f90;
}
.txt--bold {
    font-family: Poppins, sans-serif !important;
	font-weight: 500;
}

h1 {
    line-height: 30px !important;
    font-family: Poppins, sans-serif !important;
    margin: 10px 0 15px;
    font-size: 25px !important;
}
.article--mainPhotoSource {
    position: static;
    transform: none;
    text-shadow: none;
    color: #595959;
    font-size: 14px;
    line-height: 1.4;
    font-weight: 300;
    display: block;
    margin: 5px 0 0;
    -ms-writing-mode: initial;
    writing-mode: initial;
}
.question,
.quote,
.answer {
    margin-left: 55px;
	margin-right: 0;
	min-height: 110px;
	padding: 5px 0;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: flex-start;
	position: relative;
	margin-bottom: 20px;
}
.question {
	background: #f2f2f2;
    font-weight: bold;
}

.question:before {
	transform: translate(-50%, -50%);
	background: url(../images/content/question.jpg) center center no-repeat;
	border-radius:50%;
	overflow: hidden;
	background-size: cover;
}
.question:before,
.answer:before {
	content: '';
	display: block;
	width: 110px;
	height: 110px;
	position: absolute;
	left: 0;
	top: 50%;  
}
.answer:before {
	left: 0;
	top: 0px;
	transform: translateX(-50%);
	background: url(../images/content/answer.jpg) center center no-repeat;
	background-size: cover;
	border-radius: 50%;
}


.question p,
.answer p {
	margin: 0;
	padding-left: 75px;
	padding-right: 20px;
	margin-bottom: 15px;
}
.question p:last-of-type,
.answer p:last-of-type {
	margin-bottom: 0;
}

.question p span,
.answer p span {
	font-weight: 700;
}
.baloon {
    background-color: #f90;
    padding: 10px 20px 20px;
    margin: 0 0 30px 10px;
    position: relative;
    font-family: Poppins, sans-serif !important;
	font-weight: 700;
	color: #FFF;
}

.baloon::before {
    content: "";
    position: absolute;
    left: -10px;
    top: 0;
    width: 0;
    height: 0;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 20px solid #f90
}

.baloon .txt,.baloon .h2 {
    margin-bottom: 0
}
.benefits-list__item {
    display: flex;
    align-items: center;
    margin-bottom: 20px
}

.benefits-list__item img {
    flex-shrink: 0;
    margin-right: 10px
}

.benefits-list__item span {
    display: block
}
.benefits-list__item p {
	margin: 0;
}
@media(min-width: 768px) {
    .benefits-list__item {
        align-items: center
    }

    .benefits-list__item span {
        display: inline
    }
    .act {
		flex-direction: row;
	}
}

.quote {
    max-width: 780px;
    border: 3px solid #e02020;
    position: relative;
    padding: 20px;
    margin: 0 auto 50px;
    background: url(../images/content/quote.png) no-repeat 97% 77%;
    font-family: Poppins, sans-serif !important;
}

p.quote__txt {
    font-size: 24px;
    font-weight: 700;
    font-style: italic;
    margin-bottom: 10px;
}
p.quote__author {
    margin: 0;
    font-size: 14px;
    font-weight: 700;
}
.quote__arrow {
    content: "";
    position: absolute;
    width: 18px;
    height: 18px;
    border-top: 3px solid #e02020;
    transform: rotate(45deg);
    left: -22px;
    top: 1px;
    z-index: 2;
}

.quote::before {
    content: "";
    position: absolute;
    width: 20px;
    height: 20px;
    border-top: 3px solid #e02020;
    left: -14.6px;
    top: -3px;
}
.quote::after {
    content: "";
    position: absolute;
    width: 10px;
    height: 10px;
    border-top: 3px solid #fff;
    transform: rotate(90deg);
    left: -10px;
    top: 0;
}

.act {
    display: flex;
    flex-direction: column;
    align-items: center;
	gap: 20px;
}

.act__img {
    margin: 0 0 20px 0
}

.act__txt {
	font-family: Poppins, sans-serif !important;
    font-size: 14px;
    font-weight: 700;
    margin-bottom: 10px;
}
.red-link {
	font-family: Poppins, sans-serif !important;	
	font-weight: bold;
	font-style: italic !important;
	text-align: left;
	text-decoration: underline;
	color: #e02020!important;
	font-size: 24px;
	line-height: 1.4em;
	font-weight: 700;
	margin: 0;
	margin-bottom: 30px;
}
.red-link:hover {
	text-decoration: underline;
}
.red-link::after {
	display: inline-block;
	-webkit-animation: dotty steps(1, end) 2s infinite;
	animation: dotty steps(1, end) 2s infinite;
	content: '';
	position: absolute;
	margin-left: 10px;
}
@keyframes dotty {
	0% {
		content: '';
	}
	25% {
		content: '>';
	}
	50% {
		content: '>>';
	}
	75% {
		content: '>>>';
	}
	100% {
		content: '';
	}
}
@media (min-width: 768px) {
    .act {
        flex-direction: row;
    }
    .special-txt--big {
		font-size: 40px !important;
	}
    .flex-center {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
}
@media (max-width: 768px) {
    .red-link-v {
        text-align: center;
        margin: 0 50px;
    }
    .act__txt {
        text-align: center;
    }
}
.special-txt {
	color: #000;
}
.special-txt {
	font-family: Poppins, sans-serif !important;
    background-color: #ffce46;
    font-size: 24px;
    font-weight: 700;
    text-align: center;
    margin-bottom: 30px;
    padding: 0 10px;
	line-height: 1.4;
}
.h2--center {
    text-align: center;
    margin-bottom: 0;
}
h2 {
    font-family: Poppins, sans-serif !important;
    font-weight: 500;
    font-size: 24px;
    line-height: 1.4em;
}
.haircells {
    margin: 20px 0 50px;
    position: relative
}

.haircells .box {
	font-family: Poppins, sans-serif !important;
    position: absolute;
    padding: 10px 20px;
    font-weight: 700;
    width: 90px;
    text-align: center
}

.haircells .box--black {
    background-color: #000;
    color: #fff;
    bottom: 5px;
    left: 5px
}

.haircells .box--yellow {
    background-color: #ffce46;
    color: #000;
    bottom: 5px;
    right: 5px
}
.prod_wrapper__listy {
	background: #18182c;
}
.prod_wrapper__listy {
    text-align: left;
    padding: 20px;
    background: #f3f3f3;
    box-sizing: border-box;
    margin-bottom: 25px;
    margin-top: 100px;
}
.prod_wrapper__listy .badge {
	display: table;
	width: 130px; 
	margin: -100px auto 10px; 
}
.prod_wrapper__listy h2 {
    text-align: center;
}

.prod_wrapper__listy p {
    margin: 0 0 8px;
}
.yellow-box {
	color: #000;
}
.yellow-box {
    max-width: 600px;
    background-color: #ffce46;
    padding: 10px 20px;
    font-size: 20px;
    font-weight: 700;
    text-align: center;
    margin: 0 auto 20px;
}
.sum {
    border: 3px solid #009b4d;
	margin-bottom: 50px
}

.sum .top, .sum .bottom {
    padding: 10px 20px 0 20px
}

.sum .top {
    border-bottom: 3px solid #009b4d
}

.sum .list__item {
    position: relative;
    padding: 0 0 0px 20px
}

.sum .list__item::before {
    position: absolute;
    left: 20px;
    top: 7px;
    content: "";
    width: 10px;
    height: 10px;
    background-color: #009b4d;
    border-radius: 50%
}
ul > li {
    display: block;
    position: relative;
}
.list__item {
    margin: 1rem 0 1.5rem;
}
.list__item:before {
    display: block;
    position: absolute;
    margin-left: -21px;
    margin-top: -4px;
}
.txt--big {
    font-size: 20px;
    font-weight: 700;
}
.opinions .top {
    border: 3px solid #ffce46;
    padding: 19px;
    text-align: center;
    margin-bottom: 20px;
}
.adva {
    display: table;
    width: auto;
    margin: 25px auto 0;
	position: relative;
}
.adva ul {
	font-size: 20px;
}
.adva ul li {
	position: relative;
	margin-bottom: 0px;
	padding-left: 40px;
    margin-top: 10px;
}
.adva li::before {
    content: '';
    display: block;
    width: 24px;
    height: 24px;
    background: url(../images/content/checkmark_circle.png) center center no-repeat;
    background-size: contain;
    position: absolute;
    left: 30px;
    top: 0px;
    transform: translateY(0);
}
.steps {
	margin: 30px auto;
    padding: 30px;
    background: #ebebeb;
    border: 1px solid #e1e1e4;	
}
.steps * {
	display: table;
	text-align: center!important;
	margin-left: auto!important;
	margin-right: auto!important;	
}
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -o-transform: scale(1);
        -ms-transform: scale(1);
    }

    50% {
        transform: scale(1.05);
        -webkit-transform: scale(1.05);
        -moz-transform: scale(1.05);
        -o-transform: scale(1.05);
        -ms-transform: scale(1.05);
    }

    100% {
        transform: scale(1);
        -webkit-transform: scale(1);
        -moz-transform: scale(1);
        -o-transform: scale(1);
        -ms-transform: scale(1);
    }
}
.accordion  {
	margin: 30px auto 50px;
	max-width: 650px;
}
.accordion .accordion-item {
	border-bottom: 1px solid #dcdcdc;
}

.accordion .accordion-item button[aria-expanded='true'] {
	border-bottom: 1px solid #fff;
}

.accordion button {
	position: relative;
	display: block;
	text-align: left;
	width: 100%;
	padding: 15px 0;	
	font-size: 18px;
	font-weight: 700;
    color: #416fd6;
	border: none;
	background: none;
	outline: none;
}

.accordion button:hover,
.accordion button:focus {
	cursor: pointer;
	color: #2f3d57;
}

.accordion button:hover::after,
.accordion button:focus::after {
	cursor: pointer;
	color: #416fd6;
	border: 1px solid #dcdcdc;
}

.accordion button .accordion-title {
	padding: 1em 1.5em 1em 0;
}

.accordion button .icon {
	display: inline-block;
	position: absolute;
	top: 18px;
	right: 0;
	width: 22px;
	height: 22px;
	border: 1px solid;
	border-radius: 22px;
}

.accordion button .icon::before {
	display: block;
	position: absolute;
	content: '';
	top: 9px;
	left: 5px;
	width: 10px;
	height: 2px;
	background: currentColor;
}
.accordion button .icon::after {
	display: block;
	position: absolute;
	content: '';
	top: 5px;
	left: 9px;
	width: 2px;
	height: 10px;
	background: currentColor;
}

.accordion button[aria-expanded='true'] {
	color: #2f3d57;
}
.accordion button[aria-expanded='true'] .icon::after {
	width: 0;
}
.accordion button[aria-expanded='true'] + .accordion-content {
	opacity: 1;
	max-height: 9em;
	transition: all 200ms linear;
	will-change: opacity, max-height;
}
.accordion .accordion-content {
	opacity: 0;
	max-height: 0;
	overflow: hidden;
	transition: opacity 200ms linear, max-height 200ms linear;
	will-change: opacity, max-height;
}
.accordion .accordion-content p {
	margin: 0 0 20px;
	font-size: 16px;
}

.a3Dpqq39 {
    padding-bottom: 0;
}
.a3Dpqq39 {
    margin-top: 2px;
    position: relative;
}
.a2E5Uf5Q .a2DSIv57 {
    margin: 15px 0;
    padding: 8px 0 0;
}
.ay2tV6ip p {
    width: auto;
}
.a2DSIv57 {
    font-size: 17px;
    line-height: 26px;
    margin: 17px 0;
    padding: 8px 0 0;
    font-weight: 700;
}
.a1488wuC {
    position: relative;
    top: auto;
    bottom: auto;
    left: auto;
    padding-top: 20px;
    transform: none;
    margin-bottom: 3px;
    max-width: none;
    transition: none;
    z-index: auto;
}

.aomX3m7D {
    background-color: #f7f7f7;
    position: relative;
}
.a2lCN35y {
    position: relative;
    margin: 0;
}
.a6HJWbe8 {
    margin-bottom: 1.7rem;
}
.a2lCN35y .a2H53dzb {
    padding-left: 60px;
}
.a2lCN35y .a2H53dzb {
    padding: 0;
    padding-left: 55px;
    background-color: transparent;
}

@media (max-width: 768px) {
    .a2lCN35y .a2H53dzb {
        padding-left: 0px !important;
    }
}

.a2H53dzb {
    background-color: #f3f3f3;
    padding: 1rem;
    position: relative;
}
.a2lCN35y .aZAGLdcz {
    position: static;
    display: inline;
    font-size: 15px;
    line-height: 22px;
}
.a2lCN35y .a2oI562d, .a2lCN35y .aLkfRSB8 {
    display: block;
}
.a2lCN35y .aLkfRSB8 {
    margin-right: 5px;
}

.a2lCN35y .a2oI562d, .a2lCN35y .aLkfRSB8 {
    font-size: 15px;
    line-height: 22px;
    display: inline;
}
.aLkfRSB8 {
    font-weight: 700;
    display: inline;
    margin-right: 0.5rem;
    word-wrap: break-word;
}
.w-100 {
    width: 100% !important;
}
.justify-content-between {
    -webkit-box-pack: justify !important;
    -webkit-justify-content: space-between !important;
    -ms-flex-pack: justify !important;
    justify-content: space-between !important;
}
.d-flex {
    display: -webkit-box !important;
    display: -webkit-flex !important;
    display: -ms-flexbox !important;
    display: flex !important;
}
.a2lCN35y.a3WEDIqD .a1a6MrkE {
    top: 3px;
}
.a2lCN35y .a1a6MrkE {
    position: absolute;
    top: 3px;
    left: -15px;
    color: #b7b7b7;
    font-size: 12px;
    font-weight: 300;
    line-height: 12px;
    width: 65px;
    text-align: center;
    float: left;
}
@media (max-width: 768px) {
    .a2lCN35y .a1a6MrkE {
        position: initial !important;
    }
}
.a2lCN35y .a2oI562d, .a2lCN35y .aLkfRSB8 {
    display: block;
}
.a2lCN35y .a2oI562d {
    margin: 0;
}
.a2lCN35y .a2oI562d, .a2lCN35y .aLkfRSB8 {
    font-size: 17px;
    line-height: 22px;
    display: inline;
}
.ay2tV6ip p {
    width: auto;
}
.a2lCN35y .aKeg2pKH {
    height: 24px;
    overflow: hidden;
    margin-top: 5px;
    margin-right: 10px;
    z-index: 31;
}
.aKeg2pKH {
    position: relative;
}
.a3QTnqHE {
    left: 0;
    right: auto;
    height: 19px;
}
.aTQ8uVRx {
    position: absolute;
    right: 2rem;
    top: 50%;
    transform: translateY(-50%);
}
.a2lCN35y .a2lPxz57, .a2lCN35y .aOO2Mcxh {
    background-color: transparent;
}
.a2lCN35y .aOO2Mcxh {
    position: relative;
    overflow: visible;
    padding: 0;
}
.a1Tbuw5w, .a2lPxz57 {
    transition: all 0.2s ease-in;
}
.a2lPxz57 {
    background-color: #fff;
}
.a2lCN35y .a2IeD1GC {
    padding: 0;
    position: absolute;
    top: 0;
    left: 280px;
    transform: translateY(-100%);
    z-index: 31;
}
@media (min-width: 576px) {
    .d-sm-inline-block {
        display: inline-block !important;
    }
}
.a2IeD1GC {
    text-align: center;
    padding: 0.1rem 0;
    position: relative;
}
.a2lCN35y .a1wr3vJJ {
    display: inline-block;
    vertical-align: bottom;
}
.a2lCN35y .a1wr3vJJ .a2tw1Q-l, .a31biZiI, .a2lCN35y .a1tk8-du {
    line-height: 24px;
    vertical-align: middle;
}
.a2lCN35y .a1wr3vJJ .a2tw1Q-l {
    background: transparent;
    color: #3e3e3e;
    transition: all 0.1s ease;
    border: 1px solid transparent;
    line-height: 22px;
}
.a3SsZfIC {
    font-family: Plain, sans-serif;
    border: none;
    padding: 0;
    background-color: transparent;
    opacity: 0.5;
    color: #000;
    cursor: pointer;
    font-weight: 300;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.a31biZiI {
    font-size: 14px;
    line-height: 24px;
    height: 24px;
    padding: 0 6px;
    color: #365caa;
    opacity: 1;
    border: 1px solid #ececec;
    background-color: #fff;
}
.a2tw1Q-l {
    float: right;
}
.a2lCN35y:after {
    display: block;
    content: "";
    position: absolute;
    bottom: -3px;
    left: -20px;
    right: -20px;
    background-color: #fff;
    height: 3px;
}
.ak-0W4yM {
    position: relative;
    display: inline-block;
    max-width: 200px;
}
.ak-0W4yM {
    position: relative;
    display: inline-block;
    width: 100%;
    height: 100%;
}
.a3QTnqHE .a363wu-W svg {
    fill: #58a86a;
}
.a3QTnqHE .a2bWAj-M svg, .a3QTnqHE .a363wu-W svg {
    width: 11px;
    height: 11px;
}
.a363wu-W svg {
    fill: #5fab70;
}
.a2bWAj-M svg, .a363wu-W svg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1.1rem;
    height: 1.1rem;
    stroke-width: 0;
}
.ak-0W4yM svg {
    display: block;
    width: 100%;
    height: 100%;
    margin: auto;
}
.a2lCN35y .a1tk8-du {
    position: absolute;
    top: 0;
    left: 136px;
    font-size: 14px;
    line-height: 22px;
    border: 1px solid transparent;
    background-color: transparent;
    height: 24px;
    padding: 0 6px;
    color: #365caa;
}

.a3QTnqHE .a2bWAj-M:before, .a3QTnqHE .a363wu-W:before {
    top: 50%;
    left: calc(100% + 6px);
    padding: 0;
    font-size: 14px;
    line-height: 14px;
    color: #365caa;
    transform: translateY(-50%);
}
.a2bWAj-M:before {
    right: 0;
    transform: translateX(100%);
    padding-left: 0.5rem;
}
.a2bWAj-M:before, .a363wu-W:before {
    display: block;
    content: attr(data-votes);
    color: #365caa;
    position: absolute;
    font-size: 1.2rem;
    font-weight: 300;
}
.a363wu-W:before {
    left: 0;
    transform: translateX(-100%);
    padding-right: 0.5rem;
}
.a2bWAj-M:before, .a363wu-W:before {
    display: block;
    content: attr(data-votes);
    color: #365caa;
    position: absolute;
    font-size: 1.2rem;
    font-weight: 300;
}

.aTQ8uVRx {
    position: absolute;
    right: 2rem;
    top: 50%;
    transform: translateY(-50%);
}
.a3QTnqHE .a2bWAj-M svg {
    fill: #ea6464;
}
.a3QTnqHE .a2bWAj-M svg, .a3QTnqHE .a363wu-W svg {
    width: 11px;
    height: 11px;
}
.a2bWAj-M svg {
    fill: #eb6a6a;
}
.a2bWAj-M svg, .a363wu-W svg {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1.1rem;
    height: 1.1rem;
    stroke-width: 0;
}
.ak-0W4yM svg {
    display: block;
    width: 100%;
    height: 100%;
    margin: auto;
}
.answercomm {
    margin-left: 75px;
}
.a1a6MrkE, .a1a6MrkE img {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    overflow: hidden;
}
.comm-date {
    color: #b7b7b7 !important;
    font-size: 12px !important;
    font-weight: 300 !important;
}
.a3QTnqHE .a363wu-W {
    background-color: #e1ece3;
}
.a3QTnqHE .a2bWAj-M, .a3QTnqHE .a363wu-W {
    width: 20px;
    height: 20px;
    font-size: 14px;
    font-weight: 300;
    line-height: 20px;
    margin-right: 48px;
    margin-top: 0;
    position: relative;
}
.a2bWAj-M, .a363wu-W {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    font-size: 2rem;
    font-weight: 700;
    line-height: 2rem;
    border-radius: 50%;
    background-color: #fff;
    margin-right: 2rem;
    text-align: center;
    position: relative;
    cursor: pointer;
    margin-top: 0.3rem;
}
.a3QTnqHE .a2bWAj-M {
    background-color: #f5e2e2;
}
.a3QTnqHE .a2bWAj-M, .a3QTnqHE .a363wu-W {
    width: 20px;
    height: 20px;
    font-size: 14px;
    font-weight: 300;
    line-height: 20px;
    margin-right: 48px;
    margin-top: 0;
    position: relative;
}
.a2bWAj-M, .a363wu-W {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    font-size: 2rem;
    font-weight: 700;
    line-height: 2rem;
    border-radius: 50%;
    background-color: #fff;
    margin-right: 2rem;
    text-align: center;
    position: relative;
    cursor: pointer;
    margin-top: 0.3rem;
}
.aOO2Mcxh {
    padding: 0 0.1rem 0.1rem;
    background-color: #e7e7e7;
    overflow: hidden;
}
.a2lCN35y .a2IeD1GC:after {
    display: none;
}
.a2lCN35y:hover .a1wr3vJJ .a2tw1Q-l {
	color: #f81520;
	border-color: #ececec;
	background-color: #fff;
}
.a2lCN35y:hover .a1tk8-du {
	border: 1px solid #ececec;
	background-color: #fff;
}
.a2lCN35y:hover .a1YnfD5d {
	border: 1px solid #ececec;
	background-color: #fff;
}
.a2oI562d img {
    margin-top: 7px;
}
img {
    max-width: 100%;
}
.a2lCN35y .a2oI562d, .a2lCN35y .aLkfRSB8 {
    display: block;
}
@media only screen and (min-width: 701px) {
    .pp-cikk-header-1 {
        font-size: 40px !important;
        line-height: 50px !important;
        font-family: "Open Sans", Helvetica, Arial, sans-serif !important;
        margin: 0 !important;
    }
}

@media only screen and (max-width: 700px) {
    .pp-cikk-header-1 {
        font-size: 24px !important;
        line-height: 30px !important;
        font-family: "Open Sans", Helvetica, Arial, sans-serif !important;
        margin: 0 !important;
    }
}
.header-parts .weather .weather-icon {
    left: -10px;
}

.sdxxxc {
    display: flex;
    max-width: 100%;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding-bottom: 15px;
    gap: 0px;
}
.slider-bottom__like {
    font-style: normal;
    font-weight: 700;
    font-size: 15px !important;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #131212;
}

@media(max-width: 499px) {
    .a2lCN35y .a1wr3vJJ {
        display: none;
    }
}

.list2 {
	padding-left: 0px;
	margin-bottom: 30px;
	padding: 0 0 0 20px;
    margin-right: 20px;
}
.list2 ul {
	list-style-type: none;
	margin-left: 1.25rem;
}
.list2 li {
    display: block;
    margin-bottom: 15px;
    background: linear-gradient(95.88deg, #bfc8fe73 33.37%, #f6f6f6 86.23%);
    padding: 3px 12px;
    padding-right: 50px;
    border-left: 2px solid #0e0097;
    position: relative;
    line-height: 1.4em;
}
.list2 li:last-of-type {
	margin-bottom: 0;
}
.list2 li::before {
    content: '';
    display: block;
    width: 20px;
    height: 20px;
    position: absolute;
    left: -30px !important;
    top: 0px;
    background: url(../images/checkmark_violet.png) center center no-repeat;
    background-size: contain;
}

.quotes {
	margin: -50px 0px 0;
	margin-bottom: 30px;
	padding-top: 50px;
	background: url(../images/content/quote_open.png) top left 15px no-repeat;
	background-size: 185px;
}

.quotes__content {
	padding: 30px 30px 15px 0px;
	border: 3px solid #a8b3c9;
	background: url(../images/content/quote_close.png) bottom 25px right 55px no-repeat;
	background-size: 235px;
}
.quotes__content h2 {
	/* padding-left: 60px; */
	margin-bottom: 20px;
}
body.dark .quotes {
	background: transparent;
}
body.dark .quotes__content {
	background: transparent;
}
.quotes ul {
	list-style-type: none;
	margin: 0;
	padding: 0 20px;
}

h2 {
	padding: 0 15px 0 0;
}
.quotes li {
	padding-left: 40px;
	display: block;
	position: relative;
	margin-bottom: 25px;
}
.quotes li span {
	font-weight: 700;
}
.quotes li:first-child {
	margin-top: 0;
}
.quotes li:last-of-type {
	margin-bottom: 0;
}
.quotes li::before {
	content: '';
	display: block;
	width: 24px;
	height: 24px;
	background: url(../images/content/leaf.png) center center no-repeat;
	background-size: contain;
	position: absolute;
	left: -10px !important;
	top: 50%;
	transform: translateY(-50%);
	margin-top:0;
}
.quotes li::after {
	content: '';
	display: block;
	position: absolute;
	background: #a8b3c9;
	width: 2px;
	height: 100%;
	left: 20px;
	top: 50%;
	transform: translateY(-50%);
}

@media (max-width: 480px) {
	.quote {
		margin-left: 35px;
		padding: 5px 0;
		min-height: 95px;
	}
	.quote p {
		padding-left: 50px;
		padding-right: 10px;
	}
}

.quotes.quotes-butterfly h2,
.quotes.quotes-butterfly h3 {
	color: #000;
}
.quotes.quotes-butterfly h3 {
	font-size: 18px;
}
.quotes.quotes-butterfly li::before {
	content: '';
	display: block;
	width: 29px;
	height: 35px;
	background: url(../images/content/butterfly.png) center center no-repeat;
	background-size: contain;
	position: absolute;
	left: -12px;
	top: 50%;
	transform: translateY(-50%);
	margin-top:0;
}
.quotes.quotes-butterfly .yellow-bg {
    font-size: 18px;
}
.quotes.quotes-butterfly strong {
	color: #000;
}
.violet-bg {
    background-color: #f9891f;
    background-image: linear-gradient(315deg, #f9891f 0%, #b621fe 74%);
    color: #fff!important;
}

.list {
	text-align: left;
	border: 1px solid #36a562;
	border-radius: 6px;
	box-sizing: border-box;
	background: #eaf6ee;
	font-size: 18px;
	margin: 30px auto;
	width: auto;
	max-width: 660px;
}
.list__container {
	padding: 20px 55px;
}
.list__container > ul > li {
    line-height: 1.3em;
}
.list h2 {
	margin-top: 10px;
}

.list li {
	position: relative;
	padding-bottom: 0;
	padding-left: 0;
	margin: 0 0 10px!important;
}
.list li p {
	margin-bottom: 0;
	font-weight: 400;
}

.list li::before {
	content: '';
    display: block;
    width: 24px;
    height: 24px;
    background: url(../images/content/checkmark_circle.png) center center no-repeat;
    background-size: contain;
    position: absolute;
    left: -35px !important;
    transform: translateY(0);
}

.regaly__item {
	position: relative;
    display: grid;
    grid-template-columns: 1fr 5.5fr;
    gap: 1em;
    margin-bottom: 35px;
}

.regaly__right {
    font-size: 17px !important;
    line-height: 1.3em;
}

.blockquote2 {
	margin: 30px 0!important;
	padding: 36px 0 0 31px;
	position: relative;
}
.blockquote2:after {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	bottom: 0;
	width: 10px;
	background-image: url(../images/content/misc_path.webp);
	background-size: 4px;
}
.blockquote2:before {
	content: "";
	position: absolute;
	top: 0;
	left: 31px;
	bottom: 0;
	width: 34px;
	height: 29px;
	background: url(../images/content/misc_blockquote_icon.svg) no-repeat;
	background-size: 54px 60px;
	background-position: -10px -15px;
}
.blockquote2 p {
	color: #2a2a2a;
	line-height: 1.1!important;
	font-weight: 700;
}
@media (min-width: 740px) {
	.blockquote2 {
		margin: 0 0 40px;
		padding: 46px 0 0 44px;
	}
	.blockquote2:after {
		width: 14px;
	}
	.blockquote2:before {
		left: 44px;
		width: 34px;
		height: 26px;
		background-size: 65px auto;
		background-position: -15px -19px;
	}
	.blockquote2 p {
		font-size: 18px!important;
	}
}

.comm-img {
    width: 100%;
    max-width: 400px;
}

.new-properties__item {
    line-height: 1.3em;
}

#comments {
    padding-left: 0 !important;
    padding-right: 0 !important;
}

@media (max-width: 768px) {
    .aLkfRSB8 > span {
        display: block !important;
    }

    .a1a6MrkE {
        width: auto !important;
        padding-right: 10px;
    }

    .answercomm {
        margin-left: 50px;
    }
}
